<?php

namespace App\Models\Traits\Relationships;

use App\Models\City;
use App\Models\Country;
use App\Models\Subregion;
use App\Models\Continent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Staudenmeir\EloquentHasManyDeep\HasOneDeep;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;

trait RegionRelationships
{
    /**
     * Get the country that owns the region.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the cities for the region.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cities(): HasMany
    {
        return $this->hasMany(City::class);
    }

    /**
     * Get the subregion associated with the region through the country.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function subregion(): HasOneThrough
    {
        return $this->hasOneThrough(
            Subregion::class,     // Final model
            Country::class,      // Intermediate model
            'id',              // Foreign key on intermediate model (countries.id)
            'id',              // Foreign key on final model (subregions.id)
            'country_id',       // Local key on this model (regions.country_id)
            'subregion_id'       // Local key on intermediate model (countries.subregion_id)
        );
    }

    /**
     * Get the continent associated with the region through the country and subregion.
     * Uses HasOneDeep for deep relationship: Region -> Country -> Subregion -> Continent
     * Taking the reverse of Continent -> Subregion -> Country -> Region
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasOneDeep
     */
    public function continent(): HasOneDeep
    {
        return $this->hasOneDeepFromReverse(
            (new Continent())->regions()
        );
    }

    /**
     * Get the packages associated with the region through cities.
     * Uses HasManyDeep for deep relationship: Region -> City -> Package (many-to-many)
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function packages(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->cities(),
            (new City())->packages()
        )->distinct();
    }
}
