<?php

namespace App\Models\Traits\Relationships;

use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;

trait SubregionRelationships
{
    /**
     * Get the continent that owns the subregion.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function continent(): BelongsTo
    {
        return $this->belongsTo(Continent::class);
    }

    /**
     * Get the countries for the subregion.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function countries(): HasMany
    {
        return $this->hasMany(Country::class);
    }

    /**
     * Get the regions for the subregion through countries.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function regions(): HasManyThrough
    {
        return $this->hasManyThrough(Region::class, Country::class);
    }

    /**
     * Get the cities for the subregion through countries and regions.
     * Uses HasManyDeep for deep relationship: Subregion -> Country -> Region -> City
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function cities(): HasManyDeep
    {
        return $this->hasManyDeep(
            City::class,
            [Country::class, Region::class]
        );
    }

    /**
     * Get the packages for the subregion through cities.
     * Uses HasManyDeep for deep relationship: Subregion -> Country -> Region -> City -> Package
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function packages(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->cities(),
            (new City())->packages()
        )->distinct();
    }
}
