<?php

namespace App\Models\Traits;

use App\Models\Alias;

trait AutoAliasable
{
    /**
     * Boot the auto aliasable trait for a model.
     */
    protected static function bootAutoAliasable(): void
    {
        // Create alias when model is created
        static::created(function ($model) {
            $model->createAutoAlias();
        });

        // Create/update alias when model is updated and name has changed
        static::updated(function ($model) {
            if ($model->wasChanged('name'))
            {
                // Delete old alias if name changed
//                $oldName = $model->getOriginal('name');
//                if ($oldName) {
//                    Alias::where('name', $oldName)
//                        ->where('aliasable_type', static::class)
//                        ->where('aliasable_id', $model->id)
//                        ->delete();
//                }

                $model->createAutoAlias();
            }
        });
    }

    /**
     * Create or update an alias for this model based on its name.
     */
    protected function createAutoAlias(): void
    {
        if (empty($this->name))
        {
            return;
        }

        Alias::updateOrCreate(
            [
                'name' => $this->name,
                'aliasable_type' => static::class,
                'aliasable_id' => $this->id,
            ],
            [
                'language' => 'el',
            ]
        );
    }
}
