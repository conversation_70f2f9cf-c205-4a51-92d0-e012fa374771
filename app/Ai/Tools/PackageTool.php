<?php
namespace App\Ai\Tools;

use App\Models\Alias;
use App\Models\Package;
use App\Models\Query;
use EchoLabs\Prism\Tool;

class PackageTool extends Tool
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $this
            ->as('PackageTool')
            ->for('Displaying packages')
            ->withStringParameter('query', 'The search query for packages')
            ->using($this);
    }

    public function __invoke(string $query = '')
    {

        $normalized_query = mb_strtolower($this->removeGreekAccents($query));

        try {
            $alias = Alias::whereRaw('LOWER(' . $this->removeGreekAccents('name') . ') LIKE ?', ['%' . $normalized_query . '%'])
                ->first();


            $aliasFound = $alias ? true : false;
            $relatedPackagesCount = 0;

            // Count related packages if alias is found
            if ($aliasFound && $alias->aliasable)
            {
                try {
                    $relatedPackagesCount = $alias->aliasable->packages()->published()->count();
                } catch (\Exception $e) {
                    // If counting fails, default to 0
                    $relatedPackagesCount = 0;
                }
            }

            Query::create([
                'query' => $query,
                'alias_found' => $aliasFound,
                'related_packages_count' => $relatedPackagesCount,
            ]);

            if (!$aliasFound || !$alias->aliasable) {
                return 'error';
            }

            $packages = $alias->aliasable->packages()->published()->take(10)->orderByDesc('created_at')->get();

            // dd($packages->pluck('id'));
            foreach ($packages as $package) {

                switch ($package->type) {

                    case 'food':
                        $package->url = route('el.foodtours.show', $package);
                        break;

                    default:
                        $package->url = route('el.travel.show', $package);
                        break;
                }

            }

            return json_encode($packages->pluck('title', 'url'));

        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Normalize Greek accents for query
     */
    private function removeGreekAccents($text)
    {
        $greekAccents   = ['ά', 'έ', 'ή', 'ί', 'ό', 'ύ', 'ώ', 'ϊ', 'ΐ', 'ϋ', 'ΰ', 'Ά', 'Έ', 'Ή', 'Ί', 'Ό', 'Ύ', 'Ώ'];
        $greekNoAccents = ['α', 'ε', 'η', 'ι', 'ο', 'υ', 'ω', 'ι', 'ι', 'υ', 'υ', 'Α', 'Ε', 'Η', 'Ι', 'Ο', 'Υ', 'Ω'];

        return str_replace($greekAccents, $greekNoAccents, $text);
    }
}
