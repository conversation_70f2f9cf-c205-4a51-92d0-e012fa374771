<?php

namespace Database\Seeders;

use App\Models\Alias;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Subregion;
use Google\Cloud\Translate\V3\TranslationServiceClient;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class AliasesTranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $config = ['credentials' => storage_path('app/application_default_credentials.json')];

        $translationClient = new TranslationServiceClient($config);

        // Aliases
        Alias::chunk(100, function ($aliases) use ($translationClient)
        {
            foreach ($aliases as $alias)
            {
//                echo $continent->name . "\n";
                $content = $alias->name;
                $targetLanguage = 'en';
                $response = $translationClient->translateText(
                    [$content],
                    $targetLanguage,
                    TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
                );

                foreach ($response->getTranslations() as $key => $translation)
                {
                    // create alias and connect it with the "parent" alias
                    $translatedAlias = Alias::updateOrCreate(
                        [
                            'name' => $translation->getTranslatedText(),
                            'aliasable_type' => $alias->aliasable_type,
                            'aliasable_id' => $alias->aliasable_id,
                            'language' => $targetLanguage,
                        ],
                        [
                            'name' => $translation->getTranslatedText(),
                            'aliasable_type' => $alias->aliasable_type,
                            'aliasable_id' => $alias->aliasable_id,
                            'language' => $targetLanguage,
                        ]
                    );

                    echo "Alias created: " . $translation->getTranslatedText() . "\n";
                }
            }
        });
    }
}
