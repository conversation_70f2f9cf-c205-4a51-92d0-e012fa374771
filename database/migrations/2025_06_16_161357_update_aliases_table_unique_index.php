<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aliases', function (Blueprint $table) {
            // Drop the existing unique index on name and aliasable_type
            $table->dropUnique(['name', 'aliasable_type']);

            // Add new unique index on name, aliasable_type, and aliasable_id
            $table->unique(['name', 'aliasable_type', 'aliasable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aliases', function (Blueprint $table) {
            // Drop the unique index on name, aliasable_type, and aliasable_id
            $table->dropUnique(['name', 'aliasable_type', 'aliasable_id']);

            // Restore the original unique index on name and aliasable_type
            $table->unique(['name', 'aliasable_type']);
        });
    }
};
