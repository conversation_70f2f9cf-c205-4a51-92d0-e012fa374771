<?php
namespace Modules\Cerberus\Livewire\Geographies\Subregions;

use App\Models\Continent;
use App\Models\Subregion;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $continent_id;

    public function render()
    {
        $continents = Continent::orderBy('name')->get();

        $subregions = Subregion::query()
            ->with(['continent', 'countries', 'aliases'])
            ->when($this->continent_id, function ($query) {
                $query->where('continent_id', $this->continent_id);
            })
            ->orderBy('name')
            ->paginate(100);

        return view('cerberus::livewire.geographies.subregions.index', compact('continents', 'subregions'));
    }
}
