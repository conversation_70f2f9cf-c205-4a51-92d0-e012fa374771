<?php
namespace Modules\Cerberus\Livewire\Geographies\Aliases;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $type;
    public $aliasable_types = [];
    public $searchTerm;
    public $showNoPackages = false;
    public $sortField      = 'name';
    public $sortDirection  = 'asc';

    public function sortBy($field)
    {
        if ($this->sortField == $field) {
            $this->sortDirection = $this->sortDirection == 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function mount()
    {
        $this->aliasable_types = [
            'App\Models\Continent' => 'Continent',
            'App\Models\Subregion' => 'Subregion',
            'App\Models\Country'   => 'Country',
            'App\Models\Region'    => 'Region',
            'App\Models\City'      => 'City',
        ];
    }

    public function render()
    {
        $continents = Continent::orderBy('name')->get();

        $aliases = Alias::query()
            ->with(['aliasable.packages'])
            ->when($this->type, function ($query) {
                $query->where('aliasable_type', $this->type);
            })
            ->whereHasMorph('aliasable',
                [Continent::class, Subregion::class, Country::class, Region::class, City::class],
                function ($query) {
                    $query->where('name', 'LIKE', '%' . $this->searchTerm . '%')
                        ->when($this->showNoPackages, function ($query) {
                            $query->whereDoesntHave('packages');
                        });
                })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(100);

        return view('cerberus::livewire.geographies.aliases.index', compact('aliases'));
    }

}
