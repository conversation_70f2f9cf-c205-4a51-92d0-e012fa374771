<?php
namespace Modules\Cerberus\Livewire\Geographies\Aliases;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Closure;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Create extends Component
{
    public $aliasable_types;

    #[Validate('required', message: 'Δεν επιλέξατε')]
    public $aliasable_type;

    #[Validate('required', message: 'Δεν επιλέξατε')]
    public $aliasable_id;

    public $name;

    public $language = 'el';

    protected function rules()
    {
        return [
            'name' => [
                'required',
                'min:2',
                function (string $attribute, mixed $value, Closure $fail) {

                    $alias = Alias::where('name', $value)->first();

                    if ($alias) {

                        $fail("Το alias υπάρχει ήδη");

                    }
                },
            ],
            'language' => [
                'required',
                'string',
                'max:10',
            ],
        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Το πεδίο είναι απαραίτητο',
            'name.min'      => 'Το πεδίο πρέπει να έχιε τουλάχιστο 2 χαρακτήρες',
            'name.unique'   => 'Αυτό το alias υπάρχει ήδη',
            'language.required' => 'Η γλώσσα είναι απαραίτητη',
            'language.string' => 'Η γλώσσα πρέπει να είναι κείμενο',
            'language.max' => 'Η γλώσσα δεν μπορεί να υπερβαίνει τους 10 χαρακτήρες',
        ];
    }

    public function addAlias()
    {
        $this->validate();

        $alias = Alias::create([
            'name'           => $this->name,
            'language'       => $this->language,
            'aliasable_type' => $this->aliasable_type,
            'aliasable_id'   => $this->aliasable_id,
        ]);

        return redirect()->route('cerberus.aliases.edit', $alias)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    public function mount()
    {
        $this->aliasable_types = [
            'App\Models\Continent' => 'Continent',
            'App\Models\Subregion' => 'Subregion',
            'App\Models\Country'   => 'Country',
            'App\Models\Region'    => 'Region',
            'App\Models\City'      => 'City',
        ];
    }

    public function render()
    {
        $continents = Continent::all();

        $subregions = $this->getSubregions();
        $countries  = $this->getCountries();
        $regions    = $this->getRegions();
        $cities     = $this->getCities();

        return view('cerberus::livewire.geographies.aliases.create', compact('continents', 'subregions', 'countries', 'regions', 'cities'));
    }

    protected function getSubregions()
    {
        if ($this->aliasable_type == 'App\Models\Subregion') {
            return Subregion::orderBy('name')->get();
        }
    }

    protected function getCountries()
    {
        if ($this->aliasable_type == 'App\Models\Country') {
            return Country::orderBy('name')->get();
        }
    }

    protected function getRegions()
    {
        if ($this->aliasable_type == 'App\Models\Region') {
            return Region::orderBy('name')->get();
        }
    }

    protected function getCities()
    {
        if ($this->aliasable_type == 'App\Models\City') {
            return City::orderBy('name')->get();
        }
    }
}
