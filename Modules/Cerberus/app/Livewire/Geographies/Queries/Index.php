<?php
namespace Modules\Cerberus\Livewire\Geographies\Queries;

use App\Models\Query;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $searchTerm;
    public $found = null;
    public $notes = [];
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    public function updateAliasCreated(Query $query)
    {
        $query->update([
            'alias_created' => ! $query->alias_created,
        ]);

        session()->flash('success', 'Ενημερώθηκε με επιτυχία');
    }

    public function updateNotes(Query $query)
    {
        $query->update([
            'notes' => $this->notes[$query->id]
        ]);

        session()->flash('success', 'Ενημερώθηκε με επιτυχία');
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function mount()
    {
        $this->notes = Query::pluck('notes', 'id')->toArray();

    }

    public function render()
    {

        $queries = Query::query()
            ->where('query', 'LIKE', '%' . $this->searchTerm . '%')
            ->when($this->found, function($query){
                $query->where('alias_found', $this->found -1);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(100);

        return view('cerberus::livewire.geographies.queries.index', compact('queries'));
    }
}
