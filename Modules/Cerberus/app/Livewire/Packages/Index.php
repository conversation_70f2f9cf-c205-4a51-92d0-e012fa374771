<?php

namespace Modules\Cerberus\Livewire\Packages;

use App\Models\Tag;
use App\Models\Package;
use Livewire\Component;
use App\Models\MenuItem;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Cerberus\Exports\PackagesExport;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;
    public $featured;
    public $published;
    public $type;
    public $categories;
    public $category;
    public $tags;
    public $tag;
    public array $selectedPackages;
    public $selectAll = false;
    public $showNoCities = false;

    public function togglePublished(Package $package)
    {
        $package->update(['published' => !$package->published]);
    }

    public function toggleFeatured(Package $package)
    {
        $package->update(['featured' => !$package->featured]);
    }

    public function deletePackage(Package $package)
    {
        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $package->slug) {

                return redirect()->route('cerberus.packages.index')->with('error', 'Το πακέτο δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        $package->delete();

        session()->flash('success', 'Το package διαγράφηκε με επιτυχία');
    }

    public function export()
    {
        return Excel::download(new PackagesExport($this->selectedPackages), 'packages.csv', \Maatwebsite\Excel\Excel::CSV);
    }

    public function updatedSelectAll($value)
    {
        if($value) {
            $this->selectedPackages = $this->getPackages()->pluck('id')->toArray();
        } else {
            $this->selectedPackages = [];
        }
    }

    public function updatedSelectedPackages()
    {
        $this->selectAll = false;
    }

    public function mount()
    {
        $this->categories = Tag::category()->get();
        $this->tags = Tag::tag()->get();
    }

    public function render()
    {

        $packages = $this->getPackages()->paginate(50);

        return view('cerberus::livewire.packages.index', compact('packages'));
    }


    public function getPackages()
    {
        return Package::query()
            ->with('cities')
            ->where('type', 'travel')
            ->orWhere('type', 'cruise')
            ->whereLocale('title', app()->getLocale())
            ->where(function ($query) {
                $query->whereRaw('LOWER(title) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                    ->orWhereRaw('LOWER(content) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                    ->orWhereRaw('LOWER(tagline) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%']);
            })
            ->where(function ($query) {
                $this->type ? $query->where('type', $this->type) : null;
            })
            ->where(function ($query) {
                $this->category ? $query->whereHas('categories', function($q){
                    $q->where('type', 'category')->where('tags.id', $this->category);
                }) : null;
            })
            ->where(function ($query) {
                $this->tag ? $query->whereHas('tags', function($q){
                    $q->where('type', 'tag')->where('tags.id', $this->tag);
                }) : null;
            })
            ->where(function ($query) {
                $this->featured ? $query->where('featured', 1) : NULL;
            })
            ->where(function ($query) {
                $this->published ? $query->where('published', 1) : NULL;
            })
            ->when($this->showNoCities, function ($query)
            {
                $query->whereDoesntHave('cities');
            })
            ->orderByDesc('created_at');
    }
}
