<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class AliasUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'name' => [
                'required',
                'min:2',
                Rule::unique('aliases', 'name')->where('aliasable_type', $this->aliasbale_type)->ignore($this->alias),
            ],
            'language' => [
                'required',
                'string',
                'max:10',
            ]
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Το alias δεν πρέπει να είνα κενό',
            'name.min' => 'Το alias θα πρέπει να είναι τουλάχιστο 2 χαρακτήρες',
            'name.unique' => 'Το alias υπαρχει ήδη',
            'language.required' => 'Η γλώσσα είναι απαραίτητη',
            'language.string' => 'Η γλώσσα πρέπει να είναι κείμενο',
            'language.max' => 'Η γλώσσα δεν μπορεί να υπερβαίνει τους 10 χαρακτήρες',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
