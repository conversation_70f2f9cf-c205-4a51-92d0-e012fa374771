<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Package;
use EchoLabs\Prism\Enums\Provider;
use EchoLabs\Prism\Prism;
use Illuminate\Http\Request;

class GetCitiesController extends Controller
{

    public function getPackages()
    {
        $packages = Package::with('itinerary_steps')->get();

        $chunks = $packages->chunk(20);

        return $chunks;

    }

    public function chunk(Request $request)
    {
        $chunkPackages = $this->getPackages()->get($request->chunk);

        $totalData = [];

        foreach ($chunkPackages as $package) {

            $step_data = []; // Reset step data per package

            foreach ($package->itinerary_steps as $step) {
                $step_title   = $step->getTranslation('title', 'el');
                $step_content = $step->getTranslation('content', 'el');
                $step_data[]  = [
                    'step_title'   => $step_title,
                    'step_content' => $step_content,
                ];
            }

            $data = [
                'package_id'      => $package->id,
                'package_title'   => $package->getTranslation('title', 'el'),
                'package_content' => $package->getTranslation('content', 'el'),
                'steps'           => $step_data,
            ];

            $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            $prompt = <<<EOD
            Analyze the following package data and extract a unique list of cities mentioned, together with their full geography information.

            Output only valid JSON array with this structure:

            [
                {
                    "city": "City Name",
                    "region": "Region Name",
                    "country": "Country Name",
                    "subregion": "Subregion Name",
                    "continent": "Continent Name"
                }
            ]

            - Do not include any explanations, comments, or extra text.
            - Do NOT wrap your response with backticks or markdown formatting.
            - Do NOT include package_id.
            - If no cities are found, return: []
            - If city names are in another language, translate them to Greek

            Here is the package data to analyze:

            {$json}
            EOD;

            $response = retry(3, function () use ($prompt) {
                return Prism::text()
                    ->using(Provider::OpenAI, 'gpt-4o')
                    ->withPrompt($prompt)
                    ->generate();
            }, 500);

            // Decode the AI response safely
            $decoded = json_decode($response->text, true);

            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {

                $totalData[] = [
                    'package_id' => $package->id,
                    'cities'     => array_values($decoded),
                ];

            } else {
                $totalData[] = [
                    'package_id' => $package->id,
                    'error'      => 'Invalid AI response',
                    'raw'        => $response->text,
                ];
            }

            // Optional: log usage
            echo json_encode($response->usage) . "<br/>";

            sleep(5);
        }

        // Save to file
        if (! file_exists(base_path('database/seeders/data'))) {
            mkdir(base_path('database/seeders/data'), 0755, true);
        }

        $filePath = base_path('database/seeders/data/cities_packages.json');

        // Step 1: Read existing data
        if (file_exists($filePath)) {
            $existingData = json_decode(file_get_contents($filePath), true);
            if (! is_array($existingData)) {
                $existingData = []; // handle case where file content is broken
            }
        } else {
            $existingData = [];
        }

        // Step 2: Merge new data
        $mergedData = array_merge($existingData, $totalData);

        file_put_contents($filePath, json_encode($mergedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    }
}
