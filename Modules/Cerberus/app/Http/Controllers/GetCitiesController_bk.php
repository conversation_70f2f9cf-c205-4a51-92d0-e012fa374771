<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Package;
use EchoLabs\Prism\Enums\Provider;
use EchoLabs\Prism\Prism;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class GetCitiesController extends Controller
{

    public function __invoke(): JsonResponse
    {

        $packages = Package::travel()->get();

        $data = [];
        foreach ($packages as $package) {
            $steps = $package->itinerary_steps->pluck('title')->toArray();

            $data[] = $steps;
        }

        $data = Arr::flatten($data);

        $chunks = array_chunk($data, 30);

        $cities = [];

        foreach ($chunks as $chunk) {

            $json = json_encode($chunk, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            $response = retry(3, function () use ($json) {
                return Prism::text()
                    ->using(Provider::OpenAI, 'gpt-4o')
                    ->withPrompt("Analyze the following data and return a unique list of cities mentioned, each with its region, country, subregion, continent. Respond ONLY with valid JSON, no explanation, in this format: [{\"city\": \"Αθήνα\", \"region\": \"Αττική\", \"country\": \"Ελλάδα\", \"subregion\": \"Ν. Ευρώπη\", \"continent\": \"Ευρώπη\" }]. Do not include any other text, title, or message. Only pure JSON.\n\n" . $json)
                    ->generate();
            }, 500);

            $raw = $response->text;

            // Attempt to extract just the JSON part
            if (preg_match('/\[\s*\{.*\}\s*\]/s', $raw, $matches)) {
                $json = $matches[0];
                $data = json_decode($json, true);

                $cities[] = $data;
            } else {
                $data = null;
            }

            sleep(10);
        }

        $flattened = collect($cities)->flatten(1);
        $unique    = $flattened->unique(fn($item) => $item['city'] . $item['country'])->values();

        // Save to file
        $filePath = '/data/cities.json';
        Storage::put($filePath, json_encode($unique, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return response()->json([
            'data' => $unique,
            'file' => Storage::url($filePath),
        ]);

    }
}
