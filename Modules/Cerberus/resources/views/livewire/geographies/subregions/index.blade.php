<div class="w-full px-4 block mx-auto bg-white mt-4">

    <div class="mb-4 p-4 flex justify-between">
        <div>
            <select wire:model.live='continent_id' id="" class="w-64">
                <option value="">Select Continent</option>
                @foreach ($continents as $continent)
                    <option value="{{ $continent->id }}">
                        {{ $continent->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <div>
             <x-cerberus::form.link href="{{ route('cerberus.subregions.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <div class="p-2">{{ $subregions->count() }} Subregions</div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Name</th>
            <th class="p-2">Continent</th>
            <th class="p-2">Countries</th>
            <th class="p-2">Aliases</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($subregions as $subregion)
                <tr>
                    <td class="p-2 w-12">{{ $subregion->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline"
                                href="{{ route('cerberus.subregions.edit', $subregion) }}">
                                {{ $subregion->name }}

                            </a>
                        </div>
                    </td>
                  
                    <td class="p-2">
                        {{ $subregion->continent->name }}
                    </td>
                    <td class="p-2">
                        {{ $subregion->countries->count() }}
                    </td>
                    <td class="p-2">
                        {{ $subregion->aliases->count() }}
                    </td>

                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.subregions.edit', $subregion) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $subregions->links() }}

</div>
