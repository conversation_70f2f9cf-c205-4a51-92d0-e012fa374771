<div class="w-full px-4 block mx-auto bg-white mt-4">
    <div class="mb-4 p-4 flex flex-wrap gap-3 lg:justify-between">
        <div>
            <div class="flex flex-wrap gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε χώρες</x-cerberus::form.helper>
            </div>
        </div>
        <div>
            <select wire:model.live='continent_id' id="" class="w-64">
                <option value="">Select Continent</option>
                @foreach ($continents as $continent)
                    <option value="{{ $continent->id }}">
                        {{ $continent->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <div>
            <select wire:model.live='subregion_id' id="" class="w-64">
                <option value="">Select Subregion</option>
                @foreach ($subregions as $subregion)
                    <option value="{{ $subregion->id }}">
                        {{ $subregion->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <div>
                <div class="cursor-pointer" wire:click="$toggle('showNoAliases')">
                    @if ($showNoAliases)
                        <div
                            class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                            Show All </div>
                    @else
                        <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">
                            Show No-Aliases Only </div>
                    @endif
                </div>
            </div>

        <div>
            <x-cerberus::form.link href="{{ route('cerberus.countries.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <div class="p-2">{{ $countries->count() }} Countries</div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th scope="col" class="p-2">
                ID
            </th>
            <th class="p-2" wire:click="sortBy('name')">
                <div class="cursor-pointer flex gap-2 items-center">
                    Name
                    @if ($sortField == 'name' && $sortDirection == 'asc')
                        <x-cerberus::icons.up-icon />
                    @elseif ($sortField == 'name' && $sortDirection == 'desc')
                        <x-cerberus::icons.down-icon />
                    @else
                        <div class="flex flex-col">
                            <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                        </div>
                    @endif
                </div>
            </th>
            <th class="p-2" wire:click="sortBy('iso_code')">
                <div class="cursor-pointer flex gap-2 items-center">
                    ISO
                    @if ($sortField == 'iso_code' && $sortDirection == 'asc')
                        <x-cerberus::icons.up-icon />
                    @elseif ($sortField == 'iso_code' && $sortDirection == 'desc')
                        <x-cerberus::icons.down-icon />
                    @else
                        <div class="flex flex-col">
                            <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                        </div>
                    @endif
                </div>
            </th>
            <th class="p-2">Continent</th>
            <th class="p-2">Subregion</th>
            <th class="p-2">Regions</th>
            <th class="p-2">Aliases</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($countries as $country)
                <tr>
                    <td class="p-2 w-12">{{ $country->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline"
                                href="{{ route('cerberus.countries.edit', $country) }}">
                                {{ $country->name }}

                            </a>
                        </div>
                    </td>

                    <td class="p-2">
                        {{ $country->iso_code }}
                    </td>
                    <td class="p-2">
                        {{ $country->continent->name }}
                    </td>
                    <td class="p-2">
                        {{ $country->subregion->name }}
                    </td>
                    <td class="p-2">
                        {{ $country->regions->count() }}
                    </td>
                    <td class="p-2">
                        {{ $country->aliases->count() }}
                    </td>

                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.countries.edit', $country) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $countries->links() }}

</div>
