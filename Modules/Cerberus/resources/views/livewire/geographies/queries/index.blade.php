<div class="w-full px-4 block mx-auto bg-white mt-4">
    <div class="mb-4 p-4 flex flex-wrap gap-3 lg:justify-between">
        <div>
            <div class="flex flex-wrap gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε χώρες</x-cerberus::form.helper>
            </div>
        </div>

        <div>
            <select wire:model.live='found' id="">
                <option value="0" @selected($found == 0)>All</option>
                <option value="1" @selected($found == 1)>Not Found</option>
                <option value="2" @selected($found == 2)>Found</option>
            </select>
        </div>

    </div>


    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th scope="col" class="p-2" wire:click="sortBy('id')">
                <div class="cursor-pointer flex gap-2 items-center">
                    ID
                    @if($sortField === 'id')
                        @if($sortDirection === 'asc')
                            ↑
                        @else
                            ↓
                        @endif
                    @endif
                </div>
            </th>
            <th class="p-2" wire:click="sortBy('query')">
                <div class="cursor-pointer flex gap-2 items-center">
                    Query
                    @if($sortField === 'query')
                        @if($sortDirection === 'asc')
                            ↑
                        @else
                            ↓
                        @endif
                    @endif
                </div>
            </th>
            <th class="p-2">Alias Found</th>
            <th class="p-2">Alias Created</th>
            <th class="p-2" wire:click="sortBy('related_packages_count')">
                <div class="cursor-pointer flex gap-2 items-center">
                    Related Packages
                    @if($sortField === 'related_packages_count')
                        @if($sortDirection === 'asc')
                            ↑
                        @else
                            ↓
                        @endif
                    @endif
                </div>
            </th>
            <th class="p-2">Notes</th>
            <th class="p-2">Query date</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($queries as $query)
                <tr>
                    <td class="p-2 w-12">{{ $query->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline" href="">
                                {{ $query->query }}
                            </a>
                        </div>
                    </td>

                    <td class="p-2">
                        <div class="w-4 h-4 rounded-full {{ $query->alias_found ? 'bg-green-500' : 'bg-red-500' }}">
                        </div>
                    </td>
                    <td class="p-2">

                        @if (!$query->alias_found)
                            <input id="alias_created" type="checkbox"
                                wire:click="updateAliasCreated({{ $query->id }})" value=""
                                class="block mx-auto rounded-lg" @checked($query->alias_created) />
                        @endif

                    </td>
                    <td class="p-2 text-center">
                        <span class="inline-flex items-center">
                            {{ $query->related_packages_count }}
                        </span>
                    </td>
                    <td class="p-2" x-data="{editNotes: false}" @click="editNotes = true" @click.outside="editNotes = false">
                        <div x-show="!editNotes">
                            {{ $query->notes }}
                        </div>
                        <div x-show="editNotes">
                            <form wire:submit.prevent="updateNotes({{ $query->id }})" class="w-full">
                                <textarea wire:model.defer="notes.{{ $query->id }}" class="w-full">

                                </textarea>

                                <div class="flex justify-between mt-2">
                                    <x-cerberus::form.info>
                                        CLick outside to close
                                       </x-cerberus::form.info>
                                    <x-cerberus::form.button class="bg-pacific-500 !text-small !p-1">Update</x-cerberus::form.button>
                                </div>


                            </form>
                        </div>

                    </td>
                    <td class="p-2">
                        {{ $query->created_at }}
                    </td>
                    <td>
                        @if (!$query->alias_found && !$query->alias_created)
                            <x-cerberus::form.link href="{{ route('cerberus.aliases.create') }}" target="_blank">Create
                                Alias</x-cerberus.form.link>
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $queries->links() }}

</div>
