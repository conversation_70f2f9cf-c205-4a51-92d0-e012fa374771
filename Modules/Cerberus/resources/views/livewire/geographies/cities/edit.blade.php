<div>

    <form wire:submit.prevent="updateCity" class="w-4/5 mx-auto">

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="country_id">
                Country
            </x-cerberus::form.label>
            <select wire:model.live="country_id" name="country_id" id="country_id">
                <option selected value>Select Country</option>
                @foreach ($countries as $country)
                    <option value="{{ $country->id }}" @selected(old('country_id') == $country->id)>
                        {{ $country->name }}
                    </option>
                @endforeach
            </select>
            <x-cerberus::form.error value="country_id" />
        </x-cerberus::form.field-block>

        @if ($country_id)
            @if ($regions->count())
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="region_id">
                        Region
                    </x-cerberus::form.label>
                    <select wire:model.live="region_id" id="region_id">
                        <option selected value="">Select Region</option>
                        @foreach ($regions as $region)
                            <option value="{{ $region->id }}" @selected(old('region_id') == $region->id)>
                                {{ $region->name }}
                            </option>
                        @endforeach
                    </select>
                    <x-cerberus::form.error value="region_id" />
                </x-cerberus::form.field-block>
            @else
                <div class="text-red-500 my-2">Δεν υπάρχουν regions στη χώρα. Θα πρέπει <a
                        href="{{ route('cerberus.regions.create') }}" target="_blank" class="underline">να
                        δημιουργήσετε</a> τουλάχιστο ένα. </div>
            @endif

        @endif

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="name">
                Name
            </x-cerberus::form.label>
            <x-cerberus::form.input id="name" wire:model.live="name" value="{{ old('name') }}" />
            <x-cerberus::form.error value="name" />
        </x-cerberus::form.field-block>
        <div class="flex items-center justify-between px-4">
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.cities.index') }}" />

    </form>
    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.cities.destroy', $city) }}" />
</div>

<div class="w-4/5 mx-auto">
    <h3 class="font-bold text-xl mb-3">Info</h3>

    <div class="lg:flex justify-between">
        <div class="lg:w-1/2 mt-4">
            <h4 class="mb-2 font-semibold text-md">Aliases: {{ $city->aliases->count() }}</h4>
            <ul>
                @foreach ($city->aliases as $alias)
                    <li class="py-1">
                        <a class="text-pacific-900 hover:text-slate-900" href="{{ route('cerberus.aliases.edit', $alias) }}">{{ $alias->name }}</a>
                    </li>
                @endforeach
            </ul>
        </div>
        <div class="lg:w-1/2 mt-4">
            <h4 class="mb-2 font-semibold text-md">Packages: {{ $city->packages->count() }}</h4>
            <ul>
                @foreach ($city->packages as $package)
                    <li class="py-1">
                        <a class="text-pacific-900 hover:text-slate-900" href="{{ route('cerberus.packages.edit', $package) }}">{{ $package->title }}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>


</div>

</div>
