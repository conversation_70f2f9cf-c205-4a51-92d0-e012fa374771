<div class="w-full px-4 block mx-auto bg-white mt-4">

    <div class="mb-4 p-4 flex flex-nowrap items-center gap-3 lg:justify-between">
        <div class="flex flex-wrap gap-3 items-center">
            <div>
                <div class="flex gap-2 items-center">
                    <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                    <x-cerberus::form.helper>Αναζήτηση σε cities</x-cerberus::form.helper>
                </div>
            </div>
            <div>
                <select wire:model.live='continent_id' id="" class="w-64">
                    <option value="">Select Continent</option>
                    @foreach ($continents as $continent)
                        <option value="{{ $continent->id }}">
                            {{ $continent->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select wire:model.live='subregion_id' id="" class="w-64">
                    <option value="">Select Subregion</option>
                    @foreach ($subregions as $subregion)
                        <option value="{{ $subregion->id }}">
                            {{ $subregion->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select wire:model.live='country_id' id="" class="w-64">
                    <option value="">Select Country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->id }}">
                            {{ $country->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select wire:model.live='region_id' id="" class="w-64">
                    <option value="">Select Region</option>
                    @foreach ($regions as $region)
                        <option value="{{ $region->id }}">
                            {{ $region->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <div class="cursor-pointer" wire:click="$toggle('showNoPackages')">
                    @if ($showNoPackages)
                        <div
                            class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                            Show All </div>
                    @else
                        <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">
                            Show No-Packages Only </div>
                    @endif
                </div>
            </div>
            <div>
                <div class="cursor-pointer" wire:click="$toggle('showNoAliases')">
                    @if ($showNoAliases)
                        <div
                            class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                            Show All </div>
                    @else
                        <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">
                            Show No-Aliases Only </div>
                    @endif
                </div>
            </div>
        </div>

        <div>
            <x-cerberus::form.link href="{{ route('cerberus.cities.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <div class="p-2">{{ $cities->count() }} Cities</div>
    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">City Name</th>
            <th class="p-2">Region</th>
            <th class="p-2">Country</th>
            <th class="p-2">Aliases</th>
            <th class="p-2">Packages</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($cities as $city)
                <tr>
                    <td class="p-2 w-12">{{ $city->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline" href="{{ route('cerberus.cities.edit', $city) }}">
                                {{ $city->name }}

                            </a>
                        </div>
                    </td>
                    <td class="p-2">
                        {{ $city->region->name }}
                    </td>

                    <td class="p-2">
                        {{ $city->country->name }}
                    </td>

                    <td class="p-2">
                        {{ $city->aliases->count() }}
                    </td>
                    <td class="p-2">
                        {{ $city->packages->count() }}
                    </td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.cities.edit', $city) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>

                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $cities->links() }}
</div>
