<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ FOOD TOUR {{ $foodtour->getTranslation('title', 'el') }} </h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <form action="{{ route('cerberus.foodtours.update', $foodtour) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')


        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons translationRoute="{{ route('cerberus.foodtours.en.edit', $foodtour) }}"
                cancelRoute="{{ route('cerberus.foodtours.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('foodtours.show', $foodtour) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                    target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ FOOD TOUR
                </x-cerberus::form.link>
            </div>


        </div>

        <div class="lg:grid lg:grid-cols-12 lg:gap-4">

            <div class="col-span-8">
                <x-cerberus::form.title-block>
                    <h3 class="text-white">MAIN CONTENT</h3>
                </x-cerberus::form.title-block>

                <div>
                    <input type="hidden" name="locale" value="{{ $locale }}">
                </div>

                <livewire:cerberus::titles.edit-title :model="$foodtour" :locale="$locale" />

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="subtitle[{{ $locale }}]">
                        SUBTITLE {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="subtitle[{{ $locale }}]" name="subtitle[{{ $locale }}]"
                        value="{{ old('subtitle.' . $locale, $foodtour->getTranslation('subtitle', $locale, false)) }}" />
                    <x-cerberus::form.error value="subtitle[el]" />
                </x-cerberus::form.field-block>


                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="content[{{ $locale }}]">
                        CONTENT {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]"
                        data-height="900" class="editor">
                        {{ old('content.' . $locale, $foodtour->getTranslation('content', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="content.'.{{ $locale }}" />
                </x-cerberus::form.field-block>


                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="tagline[{{ $locale }}]">
                        TAGLINE {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>

                    <x-cerberus::form.info>
                        Ιδανικό μέγεθος: 30-40 λέξεις
                    </x-cerberus::form.info>

                    <x-cerberus::form.textarea id="tagline[{{ $locale }}]" name="tagline[{{ $locale }}]"
                        class="h-16">
                        {{ old('tagline.' . $locale, $foodtour->getTranslation('tagline', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="tagline.' . {{ $locale }}" />
                </x-cerberus::form.field-block>


                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="info[{{ $locale }}]">
                        BEST MOMENTS {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="info[{{ $locale }}]" name="info[{{ $locale }}]"
                        class="editor">
                        {{ old('info.' . $locale, $foodtour->getTranslation('info', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="info.'.{{ $locale }}" />
                </x-cerberus::form.field-block>

                <div class="flex">
                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="included[{{ $locale }}]">
                            INCLUDED {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="included[{{ $locale }}]" data-height="500"
                            name="included[{{ $locale }}]" class="editor">
                            {{ old('included.' . $locale, $foodtour->getTranslation('included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="included.'.{{ $locale }}" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="not_included[{{ $locale }}]">
                            NOT INCLUDED {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="not_included[{{ $locale }}]" data-height="500"
                            name="not_included[{{ $locale }}]" class="editor">
                            {{ old('not_included.' . $locale, $foodtour->getTranslation('not_included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="not_included.'.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                </div>


                <x-cerberus::form.field-block>

                    {{-- Υπάρχοντα Itinerary Steps  --}}

                    <livewire:cerberus::packages.itinerary-steps :package="$foodtour" :locale="$locale"
                        :isFoodTour="true" />

                    {{-- Creating new steps  with Alpine Js --}}

                    <div x-data="{
                        items: [],
                        adding: false,
                        count: {{ $foodtour->faqs->count() }},
                        initEditor() {
                            setTimeout(() => {
                                tinymce.init({
                                    selector: '.editor',
                                    plugins: 'lists link image code',
                                    toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | code',
                                });
                            }, 0);
                        }
                    }" x-init="initEditor()" @items-updated.window="initEditor()">
                        <template x-for="item in items" :key="item">
                            <div class="border-t-2 pb-3">
                                <div class="mx-2 mb-4 bg-slate-200 py-2 px-3 rounded-md">
                                    Itinerary Step <span
                                        x-text="items.indexOf(item)+{{ $foodtour->faqs->count() + 1 }}"></span>
                                </div>
                                <div>
                                    <x-cerberus::form.label>Title</x-cerberus::form.label>
                                    <x-cerberus::form.input
                                        x-bind:name="`steps[${item+count}][title][{{ $locale }}]`"
                                        value="" />
                                </div>
                                <div>
                                    <x-cerberus::form.label>Content</x-cerberus::form.label>
                                    <x-cerberus::form.textarea
                                        x-bind:name="`steps[${item+count}][content][{{ $locale }}]`"
                                        value="" class="editor">
                                    </x-cerberus::form.textarea>
                                </div>

                                <button @click.prevent="items.splice(items.indexOf(item), 1).sort()"
                                    class="ml-3 float-right bg-red-500 p-1 text-white rounded-lg">Remove</button>
                            </div>
                        </template>
                        <x-cerberus::form.button @click.prevent="items.push(items.length+1); $dispatch('items-updated')"
                            id="addStep" class="bg-slate-800 text-white mt-4">
                            <x-cerberus::icons.add-icon />
                        </x-cerberus::form.button>
                    </div>
                </x-cerberus::form.field-block>

                {{-- SEO ATTRIBUTES --}}
                <x-cerberus::seo.seo-attributes :model="$foodtour" :locale="$locale" />

                {{-- SEO CHECKER --}}
                <x-cerberus::seo.seo-checker :model="$foodtour" />

            </div>
            <div class="col-span-4">
                <x-cerberus::form.title-block class="p-3">
                    <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                </x-cerberus::form.title-block>

                <x-cerberus::form.field-block class="p-3">
                    <x-cerberus::form.label for="image">
                        ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                        @if ($foodtour->mainImage())
                            <img src="{{ asset('storage/' . $foodtour->imageSm()) }}" alt="">
                            <label class="block mb-3" for="deleteImage">
                                Επιλέξτε για διαγραφή της εικόνας
                                <input type="checkbox" name="deleteImage" id="deleteImage">
                            </label>
                        @endif
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="image" name="image" type="file"
                        value="{{ $foodtour->mainImage() }}" />
                    <x-cerberus::form.error value="image" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="alt[{{ $locale }}]">
                        ALT TAG {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                        value="{{ old('alt.' . $locale, $foodtour?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                    <x-cerberus::form.error value="alt.{{ $locale }}" />

                </x-cerberus::form.field-block>


                <x-cerberus::form.field-block>
                    <x-cerberus::form.link
                        class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                        href="{{ route('cerberus.foodtour.show.gallery', $foodtour) }}">
                        IMAGE GALLERY ({{ $foodtour->latestGallery?->images->count() }}
                        {{ $foodtour->latestGallery?->images->count() > 1 ? 'IMAGES' : 'IMAGE' }})
                    </x-cerberus::form.link>
                </x-cerberus::form.field-block>

                <x-cerberus::form.title-block>
                    <h3 class="text-white">CITIES</h3>
                </x-cerberus::form.title-block>

                <div>
                    <livewire:cerberus::packages.cities :locale="$locale" :package="$foodtour" />
                </div>

                <x-cerberus::form.title-block>
                    <h3 class="text-white">PRODUCT OPTIONS</h3>
                </x-cerberus::form.title-block>

                <div class="flex flex-wrap items-end">
                    <x-cerberus::form.field-block class=lg:w-1/2>
                        <x-cerberus::form.label for="location">
                            LOCATION
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="location" name="location"
                            value="{{ old('location', $foodtour->location) }}" />
                        <x-cerberus::form.error value="location" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class=lg:w-1/2>
                        <x-cerberus::form.label for="duration">
                            DURATION <span class="text-xs">(HOURS)</span>
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="duration" name="duration" type="number"
                            value="{{ old('duration', $foodtour->duration) }}" />
                        <x-cerberus::form.error value="duration" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class=lg:w-full>
                        <x-cerberus::form.label for="group_size">
                            GROUP SIZE <span class="text-xs">(PERSONS)</span>
                            <x-cerberus::form.info> 0 για Απεριόριστο. Κενό για να μην
                                εμφανίζεται</x-cerberus::form.info>
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="group_size" name="group_size" type="number" class="!w-1/3"
                            value="{{ old('group_size', $foodtour->group_size) }}" />
                        <x-cerberus::form.error value="group_size" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class=lg:w-full>
                        <x-cerberus::form.label for="tour_languages">
                            TOUR LANGUAGES
                        </x-cerberus::form.label>
                        <x-cerberus::form.info>Προεπιλογή: Ελληνικά, Αγγλικά</x-cerberus::form.info>
                        <select name="tour_languages[]" id="tour_laguages" multiple class="w-full h-16">
                            <option value="greek" @selected(collect(explode(', ', $foodtour->tour_languages))->contains('greek'))>{{ __('base.el') }}</option>
                            <option value="english" @selected(collect(explode(', ', $foodtour->tour_languages))->contains('english'))>{{ __('base.en') }}</option>
                        </select>
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class=lg:w-1/2>
                        <x-cerberus::form.label for="sku">
                            SKU
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="sku" name="sku"
                            value="{{ old('sku', $foodtour->sku) }}" />
                        <x-cerberus::form.error value="sku" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class=lg:w-1/2>
                        <x-cerberus::form.label for="price">
                            PRICE
                        </x-cerberus::form.label>
                        <div class="flex items-center gap-1">
                            <x-cerberus::form.input id="price" name="price"
                                value="{{ old('price', $foodtour->price) }}" class="text-right" /> <span
                                class="inline-flex">€</span>
                        </div>
                        <x-cerberus::form.error value="price" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="price_fixed">
                            PRICE FIXED
                        </x-cerberus::form.label>
                        <input id="price_fixed" name="price_fixed" type="checkbox" class="rounded-full"
                            @checked($foodtour->price_fixed == 1) />
                        <x-cerberus::form.error value="price_fixed" />
                    </x-cerberus::form.field-block>
                </div>

                <x-cerberus::form.title-block>
                    <h3 class="text-white">PUBLISHING OPTIONS</h3>
                </x-cerberus::form.title-block>
                <div class="">
                    <livewire:cerberus::elements.publish-button :model="$foodtour" />
                    <livewire:cerberus::elements.featured-button :model="$foodtour" />

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΙΟΥΡΓΙΑΣ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($foodtour->created_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΤΕΛΕΥΤΑΙΑ ΕΝΗΜΕΡΩΣΗ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($foodtour->updated_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="published_at">
                            ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΟΣΙΕΥΣΗΣ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="published_at" name="published_at"
                            value="{{ old('published_at', \Carbon\Carbon::parse($foodtour->published_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>
                </div>
            </div>
        </div>


        <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.foodtours.index') }}" />


    </form>

    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.foodtours.destroy', $foodtour) }}" />

    </div>
</x-cerberus::cerberus>
