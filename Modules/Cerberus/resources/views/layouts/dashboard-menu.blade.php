<div class="p-4 ">
    @can('manage holiday packages')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.package-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('Packages') }}
            </h3>
            <ul class="uppercase"
                x-show="{{ request()->routeIs('cerberus.packages.*') ||
                request()->routeIs('cerberus.foodtours.*') ||
                request()->routeIs('cerberus.ships.*')
                    ? 'true'
                    : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.packages.index') }}" :active="request()->routeIs('cerberus.packages.*')">
                        {{ __('Travel') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.foodtours.index') }}" :active="request()->routeIs('cerberus.foodtours.*')">
                        {{ __('Food Tours') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.ships.index') }}" :active="request()->routeIs('cerberus.ships.*')">
                        {{ __('Ships') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endcan
    @can('manage posts')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.blog-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('blog.blog') }}
            </h3>
            <ul class="uppercase" x-show="{{ request()->routeIs('cerberus.posts.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.posts.index') }}" :active="request()->routeIs('cerberus.posts.*')">
                        {{ __(' Posts') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.tags-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('Taxonomies') }}
            </h3>
            <ul class="uppercase"
                x-show="{{ request()->routeIs(['cerberus.categories.*', 'cerberus.tags.*', 'cerberus.banners.order']) ? 'true' : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.categories.index') }}" :active="request()->routeIs('cerberus.categories.*')">
                        {{ __(' Categories') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.tags.index') }}" :active="request()->routeIs('cerberus.tags.*')">
                        {{ __(' Tags') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endcan
    @can('manage faqs')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.faq-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('FAQS') }}
            </h3>
            <ul class="uppercase" x-show="{{ request()->routeIs('cerberus.faqs.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.faqs.index') }}" :active="request()->routeIs('cerberus.faqs.*')">
                        {{ __(' All FAQs') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endcan

    @can('manage pages')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.pages-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('Pages') }}
            </h3>
            <ul class="uppercase"
                x-show="{{ request()->routeIs('cerberus.pages.*') ||
                request()->routeIs('cerberus.homepage.index') ||
                request()->routeIs('cerberus.sliders.*')
                    ? 'true'
                    : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.homepage.index') }}" :active="request()->routeIs('cerberus.homepage.*') ||
                        request()->routeIs('cerberus.sliders.*') ||
                        request()->routeIs('cerberus.banners.*')">
                        {{ __(' Homepage') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.pages.index') }}" :active="request()->routeIs('cerberus.pages.*')">
                        {{ __(' All Pages') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endcan

    @can('manage banners')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">
                <x-cerberus::icons.pages-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('Banners') }}
            </h3>
            <ul class="uppercase" x-show="{{ request()->routeIs('cerberus.pagebanners.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.pagebanners.index') }}" :active="request()->routeIs('cerberus.pagebanners.*')">
                        {{ __(' All Banners') }}
                    </x-cerberus::nav-link>
                </li>

            </ul>
        </div>
    @endcan


    @can('manage admins')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">

                <x-cerberus::icons.admin-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('ADMINS') }}
            </h3>
            <ul class="uppercase"
                x-show="{{ request()->routeIs('cerberus.admins.*') || request()->routeIs('cerberus.roles.*') || request()->routeIs('cerberus.permissions.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.admins.index') }}" :active="request()->routeIs('cerberus.admins.*')">
                        {{ __('Admins') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.roles.index') }}" :active="request()->routeIs('cerberus.roles.*')">
                        {{ __('Roles') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.permissions.index') }}" :active="request()->routeIs('cerberus.permissions.*')">
                        {{ __('Permissions') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endcan
    @can('manage menus')


        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">

                <x-cerberus::icons.menu-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('MENUS') }}
            </h3>
            <ul class="uppercase"
                x-show="{{ request()->routeIs('cerberus.menus.*') || request()->routeIs('cerberus.menu-items.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms>

                @foreach (\App\Models\Menu::all() as $menu)
                    <li class="p-1">
                        <x-cerberus::nav-link href="{{ route('cerberus.menu-items.index', $menu) }}" :active="Request::segment(3) == $menu->id">
                            {{ $menu->title }}
                        </x-cerberus::nav-link>
                    </li>
                @endforeach
            </ul>
        </div>
    @endcan
    @can('manage leads')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">

                <x-cerberus::icons.inbox-icon class="fill-pacific-500 hover:fill-pacific-600" />

                {{ __('LEADS') }}
            </h3>
            <ul class="uppercase" 
                x-show="{{ request()->routeIs('cerberus.leads.*') ? 'true' : 'show' }} "
                x-transition.duration.300ms
                >


                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.chatbot.index') }}" :active="request()->routeIs('cerberus.leads.chatbot.index')">
                        {{ __('CHATBOT LEADS') }}
                    </x-cerberus::nav-link>
                </li>

                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.travel') }}" :active="request()->routeIs('cerberus.leads.travel')">
                        {{ __('TRAVEL LEADS') }}
                    </x-cerberus::nav-link>
                </li>
 
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.cruise') }}" :active="request()->routeIs('cerberus.leads.cruise')">
                        {{ __('CRUISE LEADS') }}
                    </x-cerberus::nav-link>
                </li>

                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.food') }}" :active="request()->routeIs('cerberus.leads.food')">
                        {{ __('FOOD TOURS LEADS') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.contact') }}" :active="request()->routeIs('cerberus.leads.contact')">
                        {{ __('CONTACT FORM LEADS') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.leads.create_package') }}" :active="request()->routeIs('cerberus.leads.create_package')">
                        {{ __('CREATE PACKAGE FORM LEADS') }}
                    </x-cerberus::nav-link>
                </li>

            </ul>
        </div>
    @endcan

    @if (auth()->user()->type == 'dev')
        <div class="border-b border-gray-400 pb-4 mb-6" x-data="{ show: false }">
            <h3 class="text-left text-white uppercase flex gap-2 items-center hover:text-pacific-600 cursor-pointer"
                @click="show = !show">

                <x-cerberus::icons.globe-icon class="stroke-pacific-500 hover:stroke-pacific-600" />

                {{ __('GEOGRAPHIES') }}
            </h3>

            <ul class="uppercase"
                x-show="{{ 
                    request()->routeIs('cerberus.subregions.*') 
                    || request()->routeIs('cerberus.continents.*')  
                    || request()->routeIs('cerberus.countries.*')  
                    || request()->routeIs('cerberus.regions.*')
                    || request()->routeIs('cerberus.aliases.*')
                    || request()->routeIs('cerberus.queries')
                    || request()->routeIs('cerberus.cities.*')   ? 'true' : 'show' }} "
                x-transition.duration.300ms>

                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.continents.index') }}" :active="request()->routeIs('cerberus.continents.*')">
                        {{ __('CONTINENTS') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.subregions.index') }}" :active="request()->routeIs('cerberus.subregions.*')">
                        {{ __('SUBREGIONS') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.countries.index') }}" :active="request()->routeIs('cerberus.countries.*')">
                        {{ __('COUNTRIES') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.regions.index') }}" :active="request()->routeIs('cerberus.regions.*')">
                        {{ __('REGIONS') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.cities.index') }}" :active="request()->routeIs('cerberus.cities.*')">
                        {{ __('CITIES') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.aliases.index') }}" :active="request()->routeIs('cerberus.aliases.*')">
                        {{ __('ALIASES') }}
                    </x-cerberus::nav-link>
                </li>
                <li class="p-1">
                    <x-cerberus::nav-link href="{{ route('cerberus.queries') }}" :active="request()->routeIs('cerberus.queries')">
                        {{ __('QUERIES') }}
                    </x-cerberus::nav-link>
                </li>
            </ul>
        </div>
    @endif

</div>

<div class="absolute bottom-0 px-2 py-2 left-0 right-0">Developed by <a href="https://www.kokonut.gr" target="_blank"
        class="hover:text-pacific-500">Kokonut Web Services</a></div>
