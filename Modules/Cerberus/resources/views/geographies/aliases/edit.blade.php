<x-cerberus::cerberus>
    <x-slot:header>
        <h1> Επεξεργα<PERSON><PERSON><PERSON> {{ $alias->name }} </h1>
    </x-slot:header>


    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />


    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.aliases.update', $alias) }}" method="POST" class="w-4/5 mx-auto">
            @csrf
            @method('PATCH')

            <x-cerberus::form.field-block>
                <div>Geography Type: {{ Str::of($alias->aliasable_type)->explode('\\')->last() }}</div>
                <input type="hidden" name="aliasbale_type" value="{{ $alias->aliasable_type }}">
                <div>Geography Name: {{ $alias->aliasable->name }}</div>
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Alias
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $alias->name) }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="language">
                    Language
                </x-cerberus::form.label>
                <x-cerberus::form.input id="language" name="language" value="{{ old('language', $alias->language) }}" />
                <x-cerberus::form.error value="language" />
            </x-cerberus::form.field-block>

            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.aliases.index') }}" />
        </form>
        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.aliases.destroy', $alias) }}" :prompt="$similar_aliases_count == 1
            ? 'Είναι το τελευταίο alias για αυτό το μοντέλο! Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;;'
            : 'Θέλετε να προχωρήσετε σε διαγραφή;'" />
    </div>

    </div>

    <div class="w-full px-4 py-2 block mx-auto bg-white mt-4">
        <div class="w-4/5 mx-auto">
            <h3>Info</h3>
           
            <h4 class="my-2">Packages: {{ $alias->aliasable->packages->count() }}</h4>

            <ul>
                @foreach ($alias->aliasable->packages as $package)
                    <li class="flex gap-2 items-center">
                        <div class="w-3 h-3 rounded-full {{ $package->published ? 'bg-pacific-500' : 'bg-sun-500' }}"></div>
                        <a href="{{ $package->type == 'food' ? route('cerberus.foodtours.edit', $package) : route('cerberus.packages.edit', $package) }}">{{ $package->title }}</a> 
                    </li>
                @endforeach
            </ul>

        </div>
    </div>

</x-cerberus::cerberus>
