@config "../../tailwind.frontend.config.js";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @font-face {
        font-family: "Manrope";
        src: url("/resources/fonts/manrope.ttf");
    }

    @font-face {
        font-family: "Mynerve";
        src: url("/resources/fonts/mynerve.ttf");
    }

    h1 {
        @apply text-3xl md:text-5xl text-darkCyan font-semibold;
    }

    h2 {
        @apply text-2xl mb-3;
    }

    h3 {
        @apply text-xl mb-1;
    }

    p {
        @apply my-3 text-base;
    }

    strong, b {
        @apply text-pacific-500;
    }

    ul li {
        @apply py-2;
    }

    blockquote {
        @apply text-center font-funny text-4xl lg:w-1/2 mx-auto;
    }

    blockquote cite {
        @apply uppercase font-sans text-pacific-500 font-semibold;
    }

    .title-box-shadow {
        box-shadow: 10px 10px #08464c;
    }

    .button-box-shadow {
        box-shadow: 12px 12px darkgray;
    }

    .button-box-shadow2 {
        box-shadow: 12px 12px #08464c;
    }

    .hero-image {
        @apply h-[30rem] lg:h-[900px] object-cover w-full;
    }

    /* Blog Specific classes */
    .blog h4 {
        @apply text-2xl my-3 text-pacific-500;
    }

    .blog strong,
    .blog b {
        @apply font-bold text-pacific-500
    }

    .blog ul {
        @apply list-square text-base ml-4 my-3;
    }

    /* Food Tours Specific classes */
    .included ul {
        @apply list-['>'] list-outside ml-2 marker:text-pacific-500 ;
    }

    .included ul li, .not-included ul li {
        @apply pl-1;
    }

    .not-included ul {
        @apply list-['-'] list-outside ml-2 marker:text-pacific-500 ;
    }

    /* Image Gallery */
    .swiper-pagination {
        @apply hidden lg:block
    }
    .swiper-pagination-bullet {
        @apply !bg-darkCyan !w-4 !h-4 border-2 border-white !opacity-100;
    }

    .swiper-pagination-bullet-active {
        @apply !bg-pacific-500;
    }

    .swiper-pagination {
        @apply !bottom-6;
    }

    .swiper-scrollbar {
        @apply !bg-white; /* Background color of the scrollbar track */
    }
    
    .swiper-scrollbar-drag {
        @apply !bg-pacific-100; /* Color of the scrollbar thumb */
    }
    
     .grecaptcha-badge {
        position: relative !important;
        bottom: 1rem !important;
        right: auto !important;
        left: auto !important;
    }

    .rainbow-gradient-text {
      
        /* Create the rainbow gradient */
        background: linear-gradient(
          90deg,
          #FF0018, #FFA52C, #FFFF41, #008018, #0000F9, #86007D
        );

      
        /* Use the gradient as the text color */
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      
        /* For compatibility */
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .rainbow-gradient-bg {
      
        /* Create the rainbow gradient */
        background: linear-gradient(
          135deg,
          #FF0018, #FFA52C, #FFFF41, #008018, #0000F9, #86007D
        );
      }
      

      /* Chat */
      .text-chat a {
        @apply underline;
      }
}